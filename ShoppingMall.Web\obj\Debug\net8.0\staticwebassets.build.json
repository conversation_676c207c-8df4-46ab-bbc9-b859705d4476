{"Version": 1, "Hash": "JWvT0scWVZS/VoLk2h8usSoKJjYx3j/XzijN/tiYnzc=", "Source": "ShoppingMall.Web", "BasePath": "_content/ShoppingMall.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ShoppingMall.Web\\wwwroot", "Source": "ShoppingMall.Web", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "Pattern": "**"}], "Assets": [{"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/auth#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "augb3t6goh", "Integrity": "/v4rue3a88PJ1q61zKkTGKqpMje445XKwYGo/kGR2Xk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\auth.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/cart#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xfjpyzjrk3", "Integrity": "UxODGxIeAVyJi27Ca52cUWe2xBFmqk6NFOMi2Q/QF+I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\cart.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/common#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k6t8vmrr5t", "Integrity": "RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\common.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/index#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i7jnjijmz4", "Integrity": "/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\index.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/product-details#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c84xq4nezm", "Integrity": "VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\product-details.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "css/product-list#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "07zi967u3o", "Integrity": "rPPLkSlEzxFHLNEg4AuHLRmTB0kT2Q8osfgtfS5Y284=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\product-list.css"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051415340001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qokh4uiflp", "Integrity": "BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051415340001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416060001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hw1ap2h7wz", "Integrity": "3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416060001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416070001#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8co0co73td", "Integrity": "OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416070001.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/banners/bxite-F2015051416070002#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t66wtv88gg", "Integrity": "VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\banners\\bxite-F2015051416070002.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\icons\\favicon.ico", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/icons/favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7duv211uso", "Integrity": "hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\icons\\favicon.ico"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\CPU.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/CPU#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tfepd08b58", "Integrity": "p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\CPU.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\dianshi.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/dianshi#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mcac68kan4", "Integrity": "ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\dianshi.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\luyouqi.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/luyouqi#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1j99jfbvzg", "Integrity": "g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\luyouqi.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/shouji#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6i8a2md2c7", "Integrity": "cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\shouji.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji2.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/shouji2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kcc1gx74e1", "Integrity": "xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\shouji2.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\touyingyi.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/touyingyi#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tvh7n3sxrd", "Integrity": "SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\touyingyi.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\WDyinpan.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/WDyinpan#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xdjj6mqi2s", "Integrity": "5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\WDyinpan.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\win7.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/win7#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1qt27ox10", "Integrity": "7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\win7.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xiangji.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/xiangji#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tx7hrwy3ub", "Integrity": "cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\xiangji.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianka.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/xianka#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9xalzd3pbe", "Integrity": "cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\xianka.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianshiqi.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/xianshiqi#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vcnlt9rpyp", "Integrity": "gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\xianshiqi.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuban.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/zhuban#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u6l54bdsf3", "Integrity": "hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\zhuban.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuji.jpg", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "images/products/zhuji#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m0xi4xxp31", "Integrity": "WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\products\\zhuji.jpg"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "js/carousel#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4td0zeg1u6", "Integrity": "GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\carousel.js"}, {"Identity": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "SourceId": "ShoppingMall.Web", "SourceType": "Discovered", "ContentRoot": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\", "BasePath": "_content/ShoppingMall.Web", "RelativePath": "js/common#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zdz9uhxsio", "Integrity": "mRIh7L62G9Gp+M6WWHpqrBw3PfIzWTtsMh+UVauZ/3Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\common.js"}], "Endpoints": [{"Route": "css/auth.augb3t6goh.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/v4rue3a88PJ1q61zKkTGKqpMje445XKwYGo/kGR2Xk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 12:17:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "augb3t6goh"}, {"Name": "label", "Value": "css/auth.css"}, {"Name": "integrity", "Value": "sha256-/v4rue3a88PJ1q61zKkTGKqpMje445XKwYGo/kGR2Xk="}]}, {"Route": "css/auth.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20132"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/v4rue3a88PJ1q61zKkTGKqpMje445XKwYGo/kGR2Xk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 12:17:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/v4rue3a88PJ1q61zKkTGKqpMje445XKwYGo/kGR2Xk="}]}, {"Route": "css/cart.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UxODGxIeAVyJi27Ca52cUWe2xBFmqk6NFOMi2Q/QF+I=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:12:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UxODGxIeAVyJi27Ca52cUWe2xBFmqk6NFOMi2Q/QF+I="}]}, {"Route": "css/cart.xfjpyzjrk3.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UxODGxIeAVyJi27Ca52cUWe2xBFmqk6NFOMi2Q/QF+I=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:12:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xfjpyzjrk3"}, {"Name": "label", "Value": "css/cart.css"}, {"Name": "integrity", "Value": "sha256-UxODGxIeAVyJi27Ca52cUWe2xBFmqk6NFOMi2Q/QF+I="}]}, {"Route": "css/common.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6356"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:29:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw="}]}, {"Route": "css/common.k6t8vmrr5t.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6356"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:29:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6t8vmrr5t"}, {"Name": "label", "Value": "css/common.css"}, {"Name": "integrity", "Value": "sha256-RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw="}]}, {"Route": "css/index.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:36:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY="}]}, {"Route": "css/index.i7jnjijmz4.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:36:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i7jnjijmz4"}, {"Name": "label", "Value": "css/index.css"}, {"Name": "integrity", "Value": "sha256-/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY="}]}, {"Route": "css/product-details.c84xq4nezm.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c84xq4nezm"}, {"Name": "label", "Value": "css/product-details.css"}, {"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}]}, {"Route": "css/product-details.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}]}, {"Route": "css/product-list.07zi967u3o.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13860"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rPPLkSlEzxFHLNEg4AuHLRmTB0kT2Q8osfgtfS5Y284=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:51:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07zi967u3o"}, {"Name": "label", "Value": "css/product-list.css"}, {"Name": "integrity", "Value": "sha256-rPPLkSlEzxFHLNEg4AuHLRmTB0kT2Q8osfgtfS5Y284="}]}, {"Route": "css/product-list.css", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\css\\product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13860"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rPPLkSlEzxFHLNEg4AuHLRmTB0kT2Q8osfgtfS5Y284=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:51:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rPPLkSlEzxFHLNEg4AuHLRmTB0kT2Q8osfgtfS5Y284="}]}, {"Route": "images/banners/bxite-F2015051415340001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}]}, {"Route": "images/banners/bxite-F2015051415340001.qokh4uiflp.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qokh4uiflp"}, {"Name": "label", "Value": "images/banners/bxite-F2015051415340001.jpg"}, {"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}]}, {"Route": "images/banners/bxite-F2015051416060001.hw1ap2h7wz.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hw1ap2h7wz"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416060001.jpg"}, {"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}]}, {"Route": "images/banners/bxite-F2015051416060001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}]}, {"Route": "images/banners/bxite-F2015051416070001.8co0co73td.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8co0co73td"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070001.jpg"}, {"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}]}, {"Route": "images/banners/bxite-F2015051416070001.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}]}, {"Route": "images/banners/bxite-F2015051416070002.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}]}, {"Route": "images/banners/bxite-F2015051416070002.t66wtv88gg.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\banners\\bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t66wtv88gg"}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070002.jpg"}, {"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}]}, {"Route": "images/icons/favicon.7duv211uso.ico", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\icons\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25214"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 12:50:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7duv211uso"}, {"Name": "label", "Value": "images/icons/favicon.ico"}, {"Name": "integrity", "Value": "sha256-hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg="}]}, {"Route": "images/icons/favicon.ico", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\icons\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25214"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 12:50:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg="}]}, {"Route": "images/products/CPU.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\CPU.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}]}, {"Route": "images/products/CPU.tfepd08b58.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\CPU.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfepd08b58"}, {"Name": "label", "Value": "images/products/CPU.jpg"}, {"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}]}, {"Route": "images/products/dianshi.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\dianshi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}]}, {"Route": "images/products/dianshi.mcac68kan4.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\dianshi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mcac68kan4"}, {"Name": "label", "Value": "images/products/dianshi.jpg"}, {"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}]}, {"Route": "images/products/luyouqi.1j99jfbvzg.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\luyouqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1j99jfbvzg"}, {"Name": "label", "Value": "images/products/luyouqi.jpg"}, {"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}]}, {"Route": "images/products/luyouqi.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\luyouqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}]}, {"Route": "images/products/shouji.6i8a2md2c7.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6i8a2md2c7"}, {"Name": "label", "Value": "images/products/shouji.jpg"}, {"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}]}, {"Route": "images/products/shouji.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}]}, {"Route": "images/products/shouji2.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}]}, {"Route": "images/products/shouji2.kcc1gx74e1.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\shouji2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kcc1gx74e1"}, {"Name": "label", "Value": "images/products/shouji2.jpg"}, {"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}]}, {"Route": "images/products/touyingyi.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\touyingyi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}]}, {"Route": "images/products/touyingyi.tvh7n3sxrd.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\touyingyi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tvh7n3sxrd"}, {"Name": "label", "Value": "images/products/touyingyi.jpg"}, {"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}]}, {"Route": "images/products/WDyinpan.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\WDyinpan.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}]}, {"Route": "images/products/WDyinpan.xdjj6mqi2s.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\WDyinpan.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xdjj6mqi2s"}, {"Name": "label", "Value": "images/products/WDyinpan.jpg"}, {"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}]}, {"Route": "images/products/win7.e1qt27ox10.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\win7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1qt27ox10"}, {"Name": "label", "Value": "images/products/win7.jpg"}, {"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}]}, {"Route": "images/products/win7.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\win7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}]}, {"Route": "images/products/xiangji.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xiangji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}]}, {"Route": "images/products/xiangji.tx7hrwy3ub.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xiangji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tx7hrwy3ub"}, {"Name": "label", "Value": "images/products/xiangji.jpg"}, {"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}]}, {"Route": "images/products/xianka.9xalzd3pbe.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianka.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xalzd3pbe"}, {"Name": "label", "Value": "images/products/xianka.jpg"}, {"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}]}, {"Route": "images/products/xianka.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianka.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}]}, {"Route": "images/products/xianshiqi.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianshiqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}]}, {"Route": "images/products/xianshiqi.vcnlt9rpyp.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\xianshiqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vcnlt9rpyp"}, {"Name": "label", "Value": "images/products/xianshiqi.jpg"}, {"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}]}, {"Route": "images/products/zhuban.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuban.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}]}, {"Route": "images/products/zhuban.u6l54bdsf3.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuban.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u6l54bdsf3"}, {"Name": "label", "Value": "images/products/zhuban.jpg"}, {"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}]}, {"Route": "images/products/zhuji.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}]}, {"Route": "images/products/zhuji.m0xi4xxp31.jpg", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\images\\products\\zhuji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m0xi4xxp31"}, {"Name": "label", "Value": "images/products/zhuji.jpg"}, {"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}]}, {"Route": "js/carousel.4td0zeg1u6.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4td0zeg1u6"}, {"Name": "label", "Value": "js/carousel.js"}, {"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}]}, {"Route": "js/carousel.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}]}, {"Route": "js/common.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5994"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mRIh7L62G9Gp+M6WWHpqrBw3PfIzWTtsMh+UVauZ/3Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:26:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mRIh7L62G9Gp+M6WWHpqrBw3PfIzWTtsMh+UVauZ/3Q="}]}, {"Route": "js/common.zdz9uhxsio.js", "AssetFile": "E:\\接单\\C#商城\\shopping_mall\\ShoppingMall.Web\\wwwroot\\js\\common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5994"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mRIh7L62G9Gp+M6WWHpqrBw3PfIzWTtsMh+UVauZ/3Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 10:26:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdz9uhxsio"}, {"Name": "label", "Value": "js/common.js"}, {"Name": "integrity", "Value": "sha256-mRIh7L62G9Gp+M6WWHpqrBw3PfIzWTtsMh+UVauZ/3Q="}]}]}