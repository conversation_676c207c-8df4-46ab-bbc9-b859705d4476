// 添加到收藏夹
function addToFavorites() {
    const url = window.location.href;
    const title = document.title;
    
    try {
        window.external.addFavorite(url, title);
    } catch (e) {
        try {
            window.sidebar.addPanel(title, url, "");
        } catch (e) {
            alert("按 Ctrl+D 键添加到收藏夹");
        }
    }
}

// 搜索功能
function performSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchTerm = searchInput.value.trim();
    
    if (searchTerm) {
        window.location.href = `/Product/Search?q=${encodeURIComponent(searchTerm)}`;
    }
}

// 切换分类菜单
function toggleCategoryMenu() {
    const categoryList = document.querySelector('.category-list');
    categoryList.style.display = categoryList.style.display === 'none' ? 'block' : 'none';
}

// 添加到购物车
function addToCart(productId) {
    fetch('/Cart/Add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
        },
        body: JSON.stringify({ productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount(data.cartCount);
            showMessage('添加成功', 'success');
        } else {
            showMessage(data.message || '添加失败', 'error');
        }
    })
    .catch(() => {
        showMessage('添加失败，请稍后重试', 'error');
    });
}

// 添加到收藏
function addToWishlist(productId) {
    fetch('/Wishlist/Add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
        },
        body: JSON.stringify({ productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('收藏成功', 'success');
        } else {
            showMessage(data.message || '收藏失败', 'error');
        }
    })
    .catch(() => {
        showMessage('收藏失败，请稍后重试', 'error');
    });
}

// 更新购物车数量
function updateCartCount(count) {
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = count;
    }
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // 2秒后自动消失
    setTimeout(() => {
        messageDiv.remove();
    }, 2000);
}

// 监听搜索框回车事件
document.querySelector('.search-input')?.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        performSearch();
    }
}); 