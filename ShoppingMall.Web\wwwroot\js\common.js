// 添加到收藏夹
function addToFavorites() {
    const url = window.location.href;
    const title = document.title;
    
    try {
        window.external.addFavorite(url, title);
    } catch (e) {
        try {
            window.sidebar.addPanel(title, url, "");
        } catch (e) {
            alert("按 Ctrl+D 键添加到收藏夹");
        }
    }
}

// 搜索功能
function performSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchTerm = searchInput.value.trim();
    
    if (searchTerm) {
        window.location.href = `/Product/Search?q=${encodeURIComponent(searchTerm)}`;
    }
}

// 切换分类菜单
function toggleCategoryMenu() {
    const categoryList = document.querySelector('.category-list');
    categoryList.style.display = categoryList.style.display === 'none' ? 'block' : 'none';
}

// 添加到购物车
function addToCart(productId) {
    // 检查用户是否登录
    if (!isUserLoggedIn()) {
        showMessage('请先登录', 'warning');
        window.location.href = '/Account/Login';
        return;
    }

    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

    fetch('/Cart/Add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
        },
        body: JSON.stringify({ productId, quantity: 1 })
    })
    .then(response => {
        if (response.status === 401) {
            showMessage('请先登录', 'warning');
            window.location.href = '/Account/Login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (data && data.success) {
            updateCartCount(data.cartCount);
            showMessage('添加成功', 'success');
        } else {
            showMessage(data?.message || '添加失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('添加失败，请稍后重试', 'error');
    });
}

// 添加到收藏
function addToWishlist(productId) {
    fetch('/Wishlist/Add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
        },
        body: JSON.stringify({ productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('收藏成功', 'success');
        } else {
            showMessage(data.message || '收藏失败', 'error');
        }
    })
    .catch(() => {
        showMessage('收藏失败，请稍后重试', 'error');
    });
}

// 更新购物车数量
function updateCartCount(count) {
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = count;
    }
}

// 检查用户是否登录
function isUserLoggedIn() {
    // 检查页面中是否有登录用户的标识
    const loginPartial = document.querySelector('.top-links');
    return loginPartial && loginPartial.textContent.includes('退出');
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;

    // 设置样式
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;

    // 根据类型设置背景色
    switch(type) {
        case 'success':
            messageDiv.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#f44336';
            break;
        case 'warning':
            messageDiv.style.backgroundColor = '#ff9800';
            break;
        default:
            messageDiv.style.backgroundColor = '#2196F3';
    }

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                messageDiv.remove();
            }, 300);
        }
    }, 3000);
}

// 添加CSS动画
if (!document.querySelector('#message-animations')) {
    const style = document.createElement('style');
    style.id = 'message-animations';
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // 2秒后自动消失
    setTimeout(() => {
        messageDiv.remove();
    }, 2000);
}

// 监听搜索框回车事件
document.querySelector('.search-input')?.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        performSearch();
    }
}); 