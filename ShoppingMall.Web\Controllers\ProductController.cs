using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ShoppingMall.Web.Data;
using ShoppingMall.Web.Models;

namespace ShoppingMall.Web.Controllers;

public class ProductController : Controller
{
    private readonly ApplicationDbContext _context;
    private const int PageSize = 12;

    public ProductController(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IActionResult> Index(
        string? category = null,
        string? brand = null,
        string? searchTerm = null,
        string? sortBy = null,
        decimal? minPrice = null,
        decimal? maxPrice = null,
        int page = 1)
    {
        var query = _context.Products.AsQueryable();

        // 应用筛选条件
        if (!string.IsNullOrEmpty(category))
            query = query.Where(p => p.Category == category);

        if (!string.IsNullOrEmpty(brand))
            query = query.Where(p => p.Brand == brand);

        if (!string.IsNullOrEmpty(searchTerm))
            query = query.Where(p => p.Name.Contains(searchTerm) || p.Description.Contains(searchTerm));

        // 价格筛选
        if (minPrice.HasValue)
            query = query.Where(p => p.Price >= minPrice.Value);

        if (maxPrice.HasValue)
            query = query.Where(p => p.Price <= maxPrice.Value);

        // 应用排序
        query = sortBy switch
        {
            "price_asc" => query.OrderBy(p => p.Price),
            "price_desc" => query.OrderByDescending(p => p.Price),
            "newest" => query.OrderByDescending(p => p.CreatedAt),
            "hot" => query.Where(p => p.IsHot).OrderByDescending(p => p.SalesCount),
            "discount" => query.Where(p => p.HasDiscount).OrderByDescending(p => p.DiscountPercentage),
            _ => query.OrderByDescending(p => p.CreatedAt)
        };

        // 计算分页
        var totalItems = await query.CountAsync();
        var products = await query
            .Skip((page - 1) * PageSize)
            .Take(PageSize)
            .ToListAsync();

        var viewModel = new ProductListViewModel
        {
            Products = products,
            PageNumber = page,
            PageSize = PageSize,
            TotalItems = totalItems,
            Category = category,
            Brand = brand,
            SearchTerm = searchTerm,
            SortBy = sortBy,
            MinPrice = minPrice,
            MaxPrice = maxPrice
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var product = await _context.Products
            .Include(p => p.Reviews)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (product == null)
            return NotFound();

        // 获取相关商品
        var relatedProducts = await _context.Products
            .Where(p => p.Category == product.Category && p.Id != product.Id)
            .Take(4)
            .ToListAsync();

        var viewModel = new ProductDetailsViewModel
        {
            Product = product,
            RelatedProducts = relatedProducts
        };

        return View(viewModel);
    }
} 