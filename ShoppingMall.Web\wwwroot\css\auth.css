    justify-content: center;
    padding: 20px;
}

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    position: relative;
    overflow: hidden;
}

/* 动态背景效果 */
.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite;
    pointer-events: none;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 主容器 */
.auth-container {
    width: 100%;
    max-width: 1200px;
    min-height: 700px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
    animation: containerFadeIn 0.8s ease-out;
}

@keyframes containerFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 左侧装饰区域 */
.auth-decoration {
    padding: 60px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-decoration::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.decoration-content {
    max-width: 350px;
    position: relative;
    z-index: 2;
}

.decoration-icon {
    font-size: 64px;
    margin-bottom: 30px;
    display: inline-block;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.decoration-title {
    font-size: 36px;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.decoration-subtitle {
    font-size: 18px;
    opacity: 0.95;
    line-height: 1.6;
    font-weight: 300;
}

/* 右侧表单区域 */
.auth-form-section {
    padding: 60px 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    position: relative;
}

.auth-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(229, 62, 62, 0.1), rgba(197, 48, 48, 0.1));
    border-radius: 50%;
    transform: translate(50%, -50%);
}
