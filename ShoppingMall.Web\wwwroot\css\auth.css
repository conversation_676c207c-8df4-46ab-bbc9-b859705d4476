/* 认证页面布局 */
.auth-page-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: var(--background-color);
}

.auth-container {
    width: 100%;
    max-width: 1000px;
    min-height: 600px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* 左侧装饰区域 */
.auth-decoration {
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.decoration-content {
    max-width: 300px;
}

.decoration-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.decoration-title {
    font-size: 32px;
    margin-bottom: 10px;
}

.decoration-subtitle {
    font-size: 18px;
    opacity: 0.9;
    line-height: 1.6;
}

/* 右侧表单区域 */
.auth-form-section {
    padding: 40px;
    background: white;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.auth-title {
    font-size: 24px;
    color: var(--text-color);
    margin-bottom: 10px;
}

.auth-subtitle {
    color: var(--light-text);
    font-size: 14px;
}

.auth-form {
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s;
}

.form-input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--light-text);
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 8px;
}

.checkbox-group label {
    color: var(--light-text);
    font-size: 14px;
}

.auth-btn {
    width: 100%;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.auth-btn:hover {
    background: #c53030;
}

.auth-links {
    margin-top: 20px;
    text-align: center;
    color: var(--light-text);
    font-size: 14px;
}

.auth-links a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-divider {
    margin: 0 8px;
    color: var(--border-color);
}

/* 社交登录 */
.social-login {
    margin-top: 30px;
    text-align: center;
}

.social-title {
    color: var(--light-text);
    font-size: 14px;
    margin-bottom: 15px;
    position: relative;
}

.social-title::before,
.social-title::after {
    content: "";
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background: var(--border-color);
}

.social-title::before {
    left: 0;
}

.social-title::after {
    right: 0;
}

.social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.social-btn {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.social-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.social-btn span {
    font-size: 18px;
}

/* 验证码 */
.captcha-group {
    display: flex;
    gap: 10px;
}

.captcha-input {
    flex: 1;
}

.captcha-image {
    width: 100px;
    height: 40px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: monospace;
    font-size: 18px;
    cursor: pointer;
}

.captcha-refresh {
    display: block;
    text-align: right;
    color: var(--light-text);
    font-size: 12px;
    text-decoration: none;
    margin-top: 5px;
}

/* 错误提示 */
.text-danger {
    color: var(--primary-color);
    font-size: 12px;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        grid-template-columns: 1fr;
    }

    .auth-decoration {
        display: none;
    }
} 