/* 现代化认证页面样式 */
.auth-page-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    position: relative;
    overflow: hidden;
}

/* 动态背景效果 */
.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite;
    pointer-events: none;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 主容器 */
.auth-container {
    width: 100%;
    max-width: 1200px;
    min-height: 700px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
    animation: containerFadeIn 0.8s ease-out;
}

@keyframes containerFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 左侧装饰区域 */
.auth-decoration {
    padding: 60px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-decoration::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.decoration-content {
    max-width: 350px;
    position: relative;
    z-index: 2;
}

.decoration-icon {
    font-size: 64px;
    margin-bottom: 30px;
    display: inline-block;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.decoration-title {
    font-size: 36px;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.decoration-subtitle {
    font-size: 18px;
    opacity: 0.95;
    line-height: 1.6;
    font-weight: 300;
}

/* 右侧表单区域 */
.auth-form-section {
    padding: 60px 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    position: relative;
}

.auth-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.auth-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.auth-logo {
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
}

.auth-title {
    font-size: 32px;
    color: #2d3748;
    margin-bottom: 12px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.auth-subtitle {
    color: #718096;
    font-size: 16px;
    font-weight: 400;
}

/* 表单样式 */
.auth-form {
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    background: #f7fafc;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    border-color: #e53e3e;
    outline: none;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    background: #fff;
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: #a0aec0;
}

/* 密码输入组 */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #a0aec0;
    font-size: 18px;
    transition: color 0.2s;
}

.password-toggle:hover {
    color: #e53e3e;
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    gap: 12px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #e53e3e;
}

.checkbox-group label {
    color: #4a5568;
    font-size: 14px;
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    margin-bottom: 0;
}

/* 提交按钮 */
.auth-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(229, 62, 62, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

/* 链接样式 */
.auth-links {
    text-align: center;
    margin-top: 30px;
}

.auth-links a {
    color: #e53e3e;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.auth-links a:hover {
    color: #c53030;
    text-decoration: underline;
}

/* 错误消息 */
.validation-summary-errors {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
    color: #dc2626;
}

.validation-summary-errors ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.validation-summary-errors li {
    margin-bottom: 8px;
}

/* 隐藏空的验证摘要 */
.validation-summary-valid {
    display: none !important;
}

.validation-summary-errors:empty {
    display: none !important;
}

.field-validation-error {
    color: #dc2626;
    font-size: 14px;
    margin-top: 4px;
    display: block;
}

.field-validation-valid {
    display: none !important;
}

/* 成功消息 */
.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
    color: #059669;
}

/* 社交登录样式 */
.social-login {
    margin-top: 30px;
    text-align: center;
}

.social-title {
    color: #718096;
    font-size: 14px;
    margin-bottom: 16px;
    position: relative;
}

.social-title::before,
.social-title::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background: #e2e8f0;
}

.social-title::before {
    left: 0;
}

.social-title::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.social-btn {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #fff;
    color: #4a5568;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.social-btn:hover {
    border-color: #e53e3e;
    color: #e53e3e;
    transform: translateY(-1px);
}

.social-btn .fab {
    font-size: 16px;
}

/* 分隔线样式 */
.auth-divider {
    margin: 0 12px;
    color: #a0aec0;
}

/* 验证码样式 */
.captcha-group {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
}

.captcha-input {
    flex: 1;
}

.captcha-image {
    width: 100px;
    height: 40px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #4a5568;
    letter-spacing: 2px;
}

.captcha-refresh {
    color: #e53e3e;
    font-size: 14px;
    text-decoration: none;
    margin-bottom: 24px;
    display: inline-block;
}

.captcha-refresh:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        grid-template-columns: 1fr;
        max-width: 500px;
        min-height: auto;
    }

    .auth-decoration {
        display: none;
    }

    .auth-form-section {
        padding: 40px 30px;
    }

    .auth-title {
        font-size: 28px;
    }

    .decoration-title {
        font-size: 28px;
    }

    .social-buttons {
        flex-direction: column;
    }

    .social-btn {
        width: 100%;
    }
}
