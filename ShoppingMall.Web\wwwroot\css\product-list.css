/* 商品页面布局 */
.product-page {
    padding: 20px 0;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
    padding: 10px 0;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s;
}

.breadcrumb a:hover {
    color: #c53030;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: var(--light-text);
}

.breadcrumb .current {
    color: var(--text-color);
    font-weight: 500;
}

/* 商品网格布局 */
.product-grid {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    margin-top: 20px;
}

/* 左侧筛选栏 */
.filter-sidebar {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
    transition: all 0.3s ease;
}

.filter-section {
    margin-bottom: 35px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 25px;
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.filter-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.filter-title::before {
    content: '';
    width: 4px;
    height: 16px;
    background: var(--primary-color);
    margin-right: 8px;
    border-radius: 2px;
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-list li {
    margin-bottom: 8px;
}

.filter-list a {
    color: var(--light-text);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s;
    padding: 8px 12px;
    border-radius: 6px;
    display: block;
    position: relative;
}

.filter-list a:hover {
    color: var(--primary-color);
    background: #fef2f2;
}

.filter-list a.active {
    color: var(--primary-color);
    background: #fef2f2;
    font-weight: 500;
}

.filter-list a.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

/* 价格筛选 */
.price-filter {
    margin-top: 15px;
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-direction: column;
}

.price-inputs input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.price-inputs input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.price-inputs span {
    color: var(--light-text);
    font-weight: 500;
}

.filter-btn {
    width: 100%;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.filter-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

/* 右侧商品内容 */
.product-content {
    min-height: 800px;
}

/* 结果头部 */
.results-header {
    background: white;
    border-radius: 12px;
    padding: 20px 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.results-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.results-count {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.category-info {
    font-size: 14px;
    color: var(--primary-color);
    background: #fef2f2;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-label {
    font-size: 14px;
    color: var(--light-text);
    font-weight: 500;
}

.sort-option {
    color: var(--light-text);
    text-decoration: none;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s;
    position: relative;
}

.sort-option:hover {
    color: var(--primary-color);
    background: #fef2f2;
}

.sort-option.active {
    color: var(--primary-color);
    background: #fef2f2;
    font-weight: 500;
}

.view-options {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 商品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.products-list {
    display: block;
}

.products-list .product-card {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 20px;
    padding: 20px;
}

.products-list .product-image {
    width: 200px;
    height: 150px;
}

/* 商品卡片 */
.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    max-width: 100%;
    max-height: 100%;
    width: 200px;
    height: auto;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: none;
    filter: none;
}

.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff4757;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.hot-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffa502;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.quick-view-btn,
.wishlist-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: white;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.quick-view-btn:hover,
.wishlist-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-info {
    padding: 20px;
}

.product-name {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.product-name a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.2s;
}

.product-name a:hover {
    color: var(--primary-color);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
}

.product-brand {
    color: var(--primary-color);
    font-weight: 500;
    background: #fef2f2;
    padding: 2px 8px;
    border-radius: 4px;
}

.product-stock {
    color: var(--light-text);
}

.product-price {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff4757;
}

.original-price {
    font-size: 14px;
    color: var(--light-text);
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.add-to-cart {
    flex: 1;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.add-to-cart:hover {
    background: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

/* 无商品状态 */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-products-content i {
    font-size: 48px;
    color: var(--light-text);
    margin-bottom: 20px;
}

.no-products-content h3 {
    font-size: 20px;
    color: var(--text-color);
    margin-bottom: 10px;
}

.no-products-content p {
    color: var(--light-text);
    margin-bottom: 25px;
}

.btn-primary {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary:hover {
    background: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 40px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.page-link {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    border: 1px solid #e2e8f0;
    background: white;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.2s;
    font-weight: 500;
}

.page-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: #fef2f2;
}

.page-link.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-ellipsis {
    padding: 10px 15px;
    color: var(--light-text);
    font-weight: 500;
}

/* 通知系统 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
    max-width: 300px;
}

.notification.success {
    background: #2ed573;
}

.notification.error {
    background: #ff4757;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 移动端筛选按钮 */
.filter-toggle-btn {
    display: none;
    padding: 10px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    gap: 8px;
    align-items: center;
}

/* 重复样式已移除 */

/* 移除视图切换相关样式（如 .products-list、.view-btn 等） */
.products-list { display: block; }
.view-btn, .view-options { display: none !important; }

/* 响应式设计 */
@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: 250px 1fr;
        gap: 20px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .filter-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        overflow-y: auto;
        transition: left 0.3s ease;
    }
    
    .filter-sidebar.mobile-open {
        left: 0;
    }
    
    .filter-toggle-btn {
        display: flex;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .toolbar {
        width: 100%;
        justify-content: space-between;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .product-info {
        padding: 15px;
    }
    
    .product-name {
        font-size: 14px;
    }
    
    .current-price {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .product-card {
        display: grid;
        grid-template-columns: 120px 1fr;
        gap: 15px;
    }
    
    .product-image {
        height: 120px;
    }
    
    .product-info {
        padding: 12px;
    }
    
    .product-name {
        font-size: 13px;
        margin-bottom: 8px;
    }
    
    .product-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .current-price {
        font-size: 14px;
    }
    
         .add-to-cart {
         padding: 8px 12px;
         font-size: 12px;
     }
 } 