@model ShoppingMall.Web.Models.CartViewModel
@{
    ViewData["Title"] = "购物车";
}

<link rel="stylesheet" href="~/css/cart.css" />

<div class="cart-page">
    @Html.AntiForgeryToken()
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">/</span>
            <span class="current">购物车</span>
        </div>

        @if (!Model.Items.Any())
        {
            <div class="empty-cart">
                <div class="empty-icon">🛒</div>
                <h2>购物车是空的</h2>
                <p>快去添加一些商品吧</p>
                <a href="/" class="continue-shopping">继续购物</a>
            </div>
        }
        else
        {
            <div class="cart-content">
                <!-- 购物车表格 -->
                <div class="cart-table">
                    <div class="table-header">
                        <label class="select-all">
                            <input type="checkbox" checked="@Model.AllSelected" onchange="selectAll(this.checked)" />
                            <span>全选</span>
                        </label>
                        <div class="header-item">商品信息</div>
                        <div class="header-item">单价</div>
                        <div class="header-item">数量</div>
                        <div class="header-item">小计</div>
                        <div class="header-item">操作</div>
                    </div>

                    @foreach (var item in Model.Items)
                    {
                        <div class="cart-item" data-id="@item.Id">
                            <div class="item-select">
                                <input type="checkbox" checked="@item.IsSelected" 
                                       onchange="selectItem(@item.Id, this.checked)" />
                            </div>
                            <div class="item-info">
                                <img src="@item.Product.ImageUrl" alt="@item.Product.Name" />
                                <div class="item-details">
                                    <a href="@Url.Action("Details", "Product", new { id = item.Product.Id })" 
                                       class="item-name">
                                        @item.Product.Name
                                    </a>
                                    @if (item.Product.HasDiscount)
                                    {
                                        <span class="item-discount">-@item.Product.DiscountPercentage%</span>
                                    }
                                </div>
                            </div>
                            <div class="item-price">
                                <span class="current-price">¥@item.Product.Price.ToString("F2")</span>
                                @if (item.Product.HasDiscount)
                                {
                                    <span class="original-price">¥@item.Product.OriginalPrice?.ToString("F2")</span>
                                }
                            </div>
                            <div class="item-quantity">
                                <div class="quantity-controls">
                                    <button type="button" class="quantity-btn minus" onclick="updateQuantity(@item.Id, -1)">-</button>
                                    <input type="text" value="@item.Quantity" min="1"
                                           max="@item.Product.Stock" readonly
                                           class="quantity-input" />
                                    <button type="button" class="quantity-btn plus" onclick="updateQuantity(@item.Id, 1)">+</button>
                                </div>
                                <div class="stock-info">库存：@item.Product.Stock</div>
                            </div>
                            <div class="item-subtotal">¥@item.SubTotal.ToString("F2")</div>
                            <div class="item-actions">
                                <button onclick="removeItem(@item.Id)" class="remove-btn">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    }
                </div>

                <!-- 购物车底部 -->
                <div class="cart-footer">
                    <div class="footer-left">
                        <button onclick="clearCart()" class="clear-btn">清空购物车</button>
                        <div class="selected-count">
                            已选择 <span id="selectedCount">@Model.SelectedCount</span> 件商品
                        </div>
                    </div>
                    <div class="footer-right">
                        <div class="total-price">
                            合计：<span>¥@Model.Total.ToString("F2")</span>
                        </div>
                        <button onclick="checkout()" class="checkout-btn">
                            结算 (@Model.SelectedCount)
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // 获取防伪令牌
        function getAntiForgeryToken() {
            return document.querySelector('input[name="__RequestVerificationToken"]').value;
        }

        // 全选/取消全选
        function selectAll(checked) {
            fetch('/Cart/SelectAll', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-RequestVerificationToken': getAntiForgeryToken()
                },
                body: JSON.stringify({ selected: checked })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }

        // 选择单个商品
        function selectItem(id, checked) {
            fetch('/Cart/Select', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-RequestVerificationToken': getAntiForgeryToken()
                },
                body: JSON.stringify({ id, selected: checked })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }

        // 更新商品数量
        function updateQuantity(id, delta) {
            const inputElement = document.querySelector(`.cart-item[data-id="${id}"] .quantity-input`);
            const currentQuantity = parseInt(inputElement.value);
            const newQuantity = currentQuantity + delta;

            if (newQuantity <= 0) {
                removeItem(id);
                return;
            }

            // 获取库存限制
            const stockInfo = document.querySelector(`.cart-item[data-id="${id}"] .stock-info`).textContent;
            const maxStock = parseInt(stockInfo.replace('库存：', ''));

            if (newQuantity > maxStock) {
                alert('超出库存数量');
                return;
            }

            fetch('/Cart/Update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-RequestVerificationToken': getAntiForgeryToken()
                },
                body: JSON.stringify({ id, quantity: newQuantity })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || '更新失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新失败，请重试');
            });
        }

        // 删除商品
        function removeItem(id) {
            if (!confirm('确定要删除这件商品吗？')) {
                return;
            }

            fetch('/Cart/Remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-RequestVerificationToken': getAntiForgeryToken()
                },
                body: JSON.stringify({ id })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || '删除失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除失败，请重试');
            });
        }

        // 清空购物车
        function clearCart() {
            if (!confirm('确定要清空购物车吗？')) {
                return;
            }

            fetch('/Cart/Clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-RequestVerificationToken': getAntiForgeryToken()
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || '清空失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('清空失败，请重试');
            });
        }

        // 结算
        function checkout() {
            const selectedCount = @Model.SelectedCount;
            if (selectedCount === 0) {
                alert('请选择要结算的商品');
                return;
            }
            location.href = '/Order/Checkout';
        }
    </script>
} 