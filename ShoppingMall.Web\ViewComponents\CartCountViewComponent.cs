using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ShoppingMall.Web.Data;
using ShoppingMall.Web.Models;

namespace ShoppingMall.Web.ViewComponents;

public class CartCountViewComponent : ViewComponent
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<IdentityUser> _userManager;

    public CartCountViewComponent(ApplicationDbContext context, UserManager<IdentityUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var userId = _userManager.GetUserId(HttpContext.User);
        
        if (string.IsNullOrEmpty(userId))
        {
            return View(0);
        }

        var cartCount = await _context.CartItems
            .Where(c => c.UserId == userId)
            .SumAsync(c => c.Quantity);

        return View(cartCount);
    }
}
