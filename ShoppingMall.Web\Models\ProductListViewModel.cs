using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class ProductListViewModel
{
    public List<Product> Products { get; set; } = new();
    
    public string? Category { get; set; }
    
    public string? Brand { get; set; }
    
    public string? SearchTerm { get; set; }
    
    public string? SortBy { get; set; }
    
    public decimal? MinPrice { get; set; }
    
    public decimal? MaxPrice { get; set; }
    
    public int PageNumber { get; set; } = 1;
    
    public int PageSize { get; set; } = 12;
    
    public int TotalItems { get; set; }
    
    public int TotalPages => (int)Math.Ceiling(TotalItems / (double)PageSize);
    
    public bool HasPreviousPage => PageNumber > 1;
    
    public bool HasNextPage => PageNumber < TotalPages;
} 