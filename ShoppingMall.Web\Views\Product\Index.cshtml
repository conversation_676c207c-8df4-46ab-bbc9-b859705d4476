@model ShoppingMall.Web.Models.ProductListViewModel
@{
    ViewData["Title"] = "商品列表";
}

<link rel="stylesheet" href="~/css/product-list.css" />

<div class="product-page">
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">/</span>
            <span class="current">@(Model.Category ?? "全部商品")</span>
        </div>

        <div class="product-grid">
            <!-- 左侧筛选栏 -->
            <aside class="filter-sidebar">
                <div class="filter-section">
                    <h3 class="filter-title">商品分类</h3>
                    <ul class="filter-list">
                        <li><a href="@Url.Action("Index", new { category = "computer", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "computer" ? "active" : "")">电脑配件</a></li>
                        <li><a href="@Url.Action("Index", new { category = "mobile", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "mobile" ? "active" : "")">手机配件</a></li>
                        <li><a href="@Url.Action("Index", new { category = "digital", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "digital" ? "active" : "")">数码影音</a></li>
                        <li><a href="@Url.Action("Index", new { category = "home", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "home" ? "active" : "")">家用电器</a></li>
                        <li><a href="@Url.Action("Index", new { category = "office", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "office" ? "active" : "")">办公打印</a></li>
                        <li><a href="@Url.Action("Index", new { category = "network", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "network" ? "active" : "")">网络产品</a></li>
                        <li><a href="@Url.Action("Index", new { category = "external", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "external" ? "active" : "")">外设产品</a></li>
                        <li><a href="@Url.Action("Index", new { category = "books", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "books" ? "active" : "")">图书音像</a></li>
                        <li><a href="@Url.Action("Index", new { category = "sports", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "sports" ? "active" : "")">运动户外</a></li>
                        <li><a href="@Url.Action("Index", new { category = "fashion", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Category == "fashion" ? "active" : "")">服饰鞋包</a></li>
                    </ul>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">价格区间</h3>
                    <div class="price-filter">
                                            <div class="price-inputs">
                        <input type="number" placeholder="最低价" id="minPrice" class="price-input" value="@Model.MinPrice">
                        <span>-</span>
                        <input type="number" placeholder="最高价" id="maxPrice" class="price-input" value="@Model.MaxPrice">
                    </div>
                        <button class="filter-btn" onclick="filterByPrice()">筛选</button>
                    </div>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">品牌筛选</h3>
                    <ul class="filter-list">
                        <li><a href="@Url.Action("Index", new { category = Model.Category, brand = "神舟", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Brand == "神舟" ? "active" : "")">神舟</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, brand = "乐视", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Brand == "乐视" ? "active" : "")">乐视</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, brand = "欣睿宇", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Brand == "欣睿宇" ? "active" : "")">欣睿宇</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, brand = "联想", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Brand == "联想" ? "active" : "")">联想</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, brand = "华硕", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.Brand == "华硕" ? "active" : "")">华硕</a></li>
                    </ul>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">商品状态</h3>
                    <ul class="filter-list">
                        <li><a href="@Url.Action("Index", new { category = Model.Category, sortBy = "hot", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.SortBy == "hot" ? "active" : "")">热销商品</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, sortBy = "discount", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.SortBy == "discount" ? "active" : "")">特价商品</a></li>
                        <li><a href="@Url.Action("Index", new { category = Model.Category, sortBy = "newest", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="@(Model.SortBy == "newest" ? "active" : "")">最新上架</a></li>
                    </ul>
                </div>
            </aside>

            <!-- 右侧商品列表 -->
            <div class="product-content">
                <!-- 结果统计和工具栏 -->
                <div class="results-header">
                    <div class="results-info">
                        <span class="results-count">共找到 @Model.TotalItems 件商品</span>
                        @if (!string.IsNullOrEmpty(Model.Category))
                        {
                            <span class="category-info">分类：@Model.Category</span>
                        }
                    </div>
                    <div class="toolbar">
                        <div class="sort-options">
                            <span class="sort-label">排序：</span>
                            <a href="@Url.Action("Index", new { category = Model.Category, brand = Model.Brand, sortBy = "price_asc", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="sort-option @("price_asc" == Model.SortBy ? "active" : "")">
                                价格从低到高
                            </a>
                            <a href="@Url.Action("Index", new { category = Model.Category, brand = Model.Brand, sortBy = "price_desc", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="sort-option @("price_desc" == Model.SortBy ? "active" : "")">
                                价格从高到低
                            </a>
                            <a href="@Url.Action("Index", new { category = Model.Category, brand = Model.Brand, sortBy = "newest", minPrice = Model.MinPrice, maxPrice = Model.MaxPrice })" 
                               class="sort-option @("newest" == Model.SortBy ? "active" : "")">
                                最新上架
                            </a>
                        </div>
                        <div class="view-options">
                            <button class="view-btn active" data-view="grid" title="网格视图">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="products-grid">
                    @if (Model.Products.Any())
                    {
                        @foreach (var product in Model.Products)
                        {
                            <div class="product-card">
                                <div class="product-image">
                                    @if (product.HasDiscount)
                                    {
                                        <span class="discount-badge">-@product.DiscountPercentage%</span>
                                    }
                                    @if (product.IsHot)
                                    {
                                        <span class="hot-badge">热销</span>
                                    }
                                    <a href="@Url.Action("Details", new { id = product.Id })">
                                        <img src="@product.ImageUrl" alt="@product.Name" loading="lazy" />
                                    </a>
                                    <!-- 移除 product-overlay -->
                                </div>
                                <div class="product-info">
                                    <h3 class="product-name">
                                        <a href="@Url.Action("Details", new { id = product.Id })">@product.Name</a>
                                    </h3>
                                    <div class="product-meta">
                                        <span class="product-brand">@product.Brand</span>
                                        <span class="product-stock">库存: @product.Stock</span>
                                    </div>
                                    <div class="product-price">
                                        <span class="current-price">¥@product.Price.ToString("F2")</span>
                                        @if (product.HasDiscount)
                                        {
                                            <span class="original-price">¥@product.OriginalPrice?.ToString("F2")</span>
                                        }
                                    </div>
                                    <div class="product-actions">
                                        <button class="add-to-cart" onclick="addToCart(@product.Id)">
                                            <i class="fas fa-shopping-cart"></i>
                                            加入购物车
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-products">
                            <div class="no-products-content">
                                <i class="fas fa-search"></i>
                                <h3>暂无相关商品</h3>
                                <p>抱歉，没有找到符合条件的商品</p>
                                <a href="@Url.Action("Index")" class="btn-primary">查看全部商品</a>
                            </div>
                        </div>
                    }
                </div>

                <!-- 分页 -->
                @if (Model.TotalPages > 1)
                {
                    <div class="pagination">
                        @if (Model.HasPreviousPage)
                        {
                            <a href="@Url.Action("Index", new { 
                                   page = Model.PageNumber - 1,
                                   category = Model.Category,
                                   brand = Model.Brand,
                                   sortBy = Model.SortBy,
                                   minPrice = Model.MinPrice,
                                   maxPrice = Model.MaxPrice
                               })" 
                               class="page-link">
                                <i class="fas fa-chevron-left"></i>
                                上一页
                            </a>
                        }

                        @for (var i = 1; i <= Model.TotalPages; i++)
                        {
                            if (i == Model.PageNumber)
                            {
                                <span class="page-link active">@i</span>
                            }
                            else if (i <= 3 || i > Model.TotalPages - 2 || (i >= Model.PageNumber - 1 && i <= Model.PageNumber + 1))
                            {
                                <a href="@Url.Action("Index", new { 
                                       page = i,
                                       category = Model.Category,
                                       brand = Model.Brand,
                                       sortBy = Model.SortBy,
                                       minPrice = Model.MinPrice,
                                       maxPrice = Model.MaxPrice
                                   })" 
                                   class="page-link">
                                    @i
                                </a>
                            }
                            else if (i == 4 && Model.PageNumber > 5)
                            {
                                <span class="page-ellipsis">...</span>
                            }
                            else if (i == Model.TotalPages - 2 && Model.PageNumber < Model.TotalPages - 4)
                            {
                                <span class="page-ellipsis">...</span>
                            }
                        }

                        @if (Model.HasNextPage)
                        {
                            <a href="@Url.Action("Index", new { 
                                   page = Model.PageNumber + 1,
                                   category = Model.Category,
                                   brand = Model.Brand,
                                   sortBy = Model.SortBy,
                                   minPrice = Model.MinPrice,
                                   maxPrice = Model.MaxPrice
                               })" 
                               class="page-link">
                                下一页
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function addToCart(productId) {
            fetch('/Cart/Add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify({ productId, quantity: 1 })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('已添加到购物车', 'success');
                } else {
                    showNotification('添加失败，请重试', 'error');
                }
            })
            .catch(error => {
                showNotification('网络错误，请重试', 'error');
            });
        }

        function addToWishlist(productId) {
            showNotification('已添加到收藏夹', 'success');
        }

        function quickView(productId) {
            window.location.href = `/Product/Details/${productId}`;
        }

        function filterByPrice() {
            const minPrice = document.getElementById('minPrice').value;
            const maxPrice = document.getElementById('maxPrice').value;
            const currentUrl = new URL(window.location);
            
            if (minPrice) currentUrl.searchParams.set('minPrice', minPrice);
            if (maxPrice) currentUrl.searchParams.set('maxPrice', maxPrice);
            
            window.location.href = currentUrl.toString();
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 切换视图模式
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const view = btn.dataset.view;
                const productsContainer = document.querySelector('.products-grid');
                productsContainer.className = `products-${view}`;
            });
        });

        // 响应式筛选栏切换
        function toggleFilterSidebar() {
            const sidebar = document.querySelector('.filter-sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 移动端筛选按钮
        if (window.innerWidth <= 768) {
            const filterToggle = document.createElement('button');
            filterToggle.className = 'filter-toggle-btn';
            filterToggle.innerHTML = '<i class="fas fa-filter"></i> 筛选';
            filterToggle.onclick = toggleFilterSidebar;
            
            const resultsHeader = document.querySelector('.results-header');
            resultsHeader.insertBefore(filterToggle, resultsHeader.firstChild);
        }
    </script>
} 