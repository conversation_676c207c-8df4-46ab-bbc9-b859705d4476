# C# 商城项目路由规划指南

## 🏠 首页访问地址
**主要入口：** `http://localhost:5002/` 或 `http://localhost:5002/Home/Index`

## 📋 完整路由结构

### 1. 🏠 首页模块 (HomeController)
| 路由 | 功能 | 说明 |
|------|------|------|
| `/` | 首页 | 默认首页，显示热门商品和分类商品 |
| `/Home/Index` | 首页 | 同上 |
| `/Home/Error` | 错误页面 | 系统错误时显示 |

### 2. 🛍️ 商品模块 (ProductController)
| 路由 | 功能 | 说明 |
|------|------|------|
| `/Product` | 商品列表 | 显示所有商品 |
| `/Product/Index` | 商品列表 | 同上 |
| `/Product?category=computer` | 分类商品 | 电脑配件分类 |
| `/Product?category=mobile` | 分类商品 | 手机配件分类 |
| `/Product?category=digital` | 分类商品 | 数码影音分类 |
| `/Product?category=home` | 分类商品 | 家用电器分类 |
| `/Product?category=office` | 分类商品 | 办公打印分类 |
| `/Product?searchTerm=关键词` | 搜索商品 | 根据关键词搜索 |
| `/Product?sortBy=price_asc` | 价格排序 | 价格从低到高 |
| `/Product?sortBy=price_desc` | 价格排序 | 价格从高到低 |
| `/Product?sortBy=newest` | 时间排序 | 最新商品 |
| `/Product/Details/{id}` | 商品详情 | 查看具体商品详情 |
| `/Product/Create` | 添加商品 | 管理员添加新商品 |

### 3. 🛒 购物车模块 (CartController) - 需要登录
| 路由 | 功能 | 说明 |
|------|------|------|
| `/Cart` | 购物车页面 | 查看购物车内容 |
| `/Cart/Index` | 购物车页面 | 同上 |
| `/Cart/Add` | 添加到购物车 | POST请求，添加商品 |
| `/Cart/Update` | 更新购物车 | POST请求，更新数量 |
| `/Cart/Remove` | 删除商品 | POST请求，删除商品 |
| `/Cart/Select` | 选择商品 | POST请求，选择/取消选择 |
| `/Cart/SelectAll` | 全选/取消全选 | POST请求 |

### 4. 📦 订单模块 (OrderController) - 需要登录
| 路由 | 功能 | 说明 |
|------|------|------|
| `/Order` | 我的订单 | 查看所有订单 |
| `/Order/Index` | 我的订单 | 同上 |
| `/Order?status=待付款` | 筛选订单 | 按状态筛选 |
| `/Order/Details/{id}` | 订单详情 | 查看具体订单 |
| `/Order/Checkout` | 结算页面 | 从购物车结算 |
| `/Order/Pay` | 支付订单 | POST请求，支付 |
| `/Order/Cancel` | 取消订单 | POST请求，取消 |

### 5. 👤 用户账户模块 (AccountController)
| 路由 | 功能 | 说明 |
|------|------|------|
| `/Account/Login` | 登录页面 | 用户登录 |
| `/Account/Register` | 注册页面 | 用户注册 |
| `/Account/Logout` | 退出登录 | POST请求，退出 |

## 🚀 推荐的访问流程

### 新用户首次访问：
1. **首页** → `http://localhost:5002/`
2. **浏览商品** → `/Product` 或点击分类链接
3. **查看商品详情** → `/Product/Details/{id}`
4. **注册账户** → `/Account/Register`
5. **登录** → `/Account/Login`
6. **添加到购物车** → 在商品页面点击"加入购物车"
7. **查看购物车** → `/Cart`
8. **结算订单** → `/Order/Checkout`
9. **查看订单** → `/Order`

### 已注册用户：
1. **首页** → `http://localhost:5002/`
2. **登录** → `/Account/Login`
3. **浏览购物** → `/Product`
4. **管理购物车** → `/Cart`
5. **管理订单** → `/Order`

## 🔗 导航链接说明

### 顶部导航栏链接：
- **首页** → `/`
- **智能本区** → `/Product?category=smart`
- **团购** → `/Product?category=group`
- **手机配件** → `/Product?category=mobile`
- **闪购** → `/Product?category=flash`
- **电脑DIY** → `/Product?category=diy`

### 侧边栏分类链接：
- **电脑配件** → `/Product?category=computer`
- **手机配件** → `/Product?category=mobile`
- **数码影音** → `/Product?category=digital`
- **家用电器** → `/Product?category=home`
- **办公打印** → `/Product?category=office`

## ⚠️ 权限说明

### 无需登录：
- 首页浏览
- 商品列表和详情
- 用户注册和登录

### 需要登录：
- 购物车功能
- 订单管理
- 商品添加到购物车
- 结算和支付

## 🎯 快速测试路径

1. **基础功能测试**：
   ```
   http://localhost:5002/ → 首页
   http://localhost:5002/Product → 商品列表
   http://localhost:5002/Product?category=computer → 电脑分类
   ```

2. **用户功能测试**：
   ```
   http://localhost:5002/Account/Register → 注册
   http://localhost:5002/Account/Login → 登录
   http://localhost:5002/Cart → 购物车
   http://localhost:5002/Order → 订单管理
   ```

## 📱 当前项目状态
- ✅ 项目运行在：`http://localhost:5002`
- ✅ 数据库已配置并迁移完成
- ✅ CSS样式已修复
- ✅ 所有路由功能正常

开始探索您的商城项目吧！🎉
