/* Global Styles */
:root {
    --primary-color: #e53e3e;
    --secondary-color: #4a5568;
    --text-color: #2d3748;
    --light-text: #718096;
    --border-color: #e2e8f0;
    --background-color: #f7fafc;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.header {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.top-bar {
    background: #f8f9fa;
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px solid var(--border-color);
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-links a {
    color: var(--light-text);
    text-decoration: none;
    margin-right: 15px;
}

.top-links a:hover {
    color: var(--primary-color);
}

.cart-info {
    display: flex;
    align-items: center;
}

.cart-count {
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 5px;
}

/* Main Header Styles */
.main-header {
    padding: 20px 0;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    text-decoration: none;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.02);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    position: relative;
    width: 60px;
    height: 60px;
}

.logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
    transition: all 0.3s ease;
}

.logo-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #e53e3e, #f56565, #e53e3e);
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.logo:hover .logo-glow {
    opacity: 0.6;
}

.logo:hover .logo-img {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(229, 62, 62, 0.3);
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.logo-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    background: linear-gradient(135deg, #e53e3e, #f56565);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
}

.logo-slogan {
    font-size: 12px;
    color: var(--light-text);
    margin: 0;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.search-box {
    display: flex;
    align-items: center;
    width: 50%;
}

.search-input {
    flex: 1;
    padding: 10px;
    border: 2px solid var(--primary-color);
    border-right: none;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
}

.search-btn {
    padding: 10px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.search-btn:hover {
    background: #c53030;
}

/* Navigation Menu Styles */
.nav-menu {
    background: var(--primary-color);
    padding: 10px 0;
}

.nav-menu .container {
    display: flex;
    align-items: center;
}

.category-dropdown {
    background: #c53030;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    margin-right: 20px;
}

.main-nav {
    display: flex;
    list-style: none;
}

.main-nav li a {
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    display: block;
}

.main-nav li a:hover {
    background: rgba(255,255,255,0.1);
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    transition: background-color 0.2s;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #c53030;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #2d3748;
}

/* Breadcrumb Navigation */
.breadcrumb {
    padding: 15px 0;
    font-size: 14px;
}

.breadcrumb-list {
    display: flex;
    list-style: none;
    align-items: center;
}

.breadcrumb-list a {
    color: var(--light-text);
    text-decoration: none;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: var(--light-text);
}

.current {
    color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
        gap: 15px;
    }

    .logo-container {
        gap: 10px;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
    }

    .logo-title {
        font-size: 24px;
    }

    .logo-slogan {
        font-size: 10px;
        letter-spacing: 1px;
    }

    .search-box {
        width: 100%;
    }

    .nav-menu .container {
        flex-direction: column;
    }

    .category-dropdown {
        width: 100%;
        margin-bottom: 10px;
    }

    .main-nav {
        flex-direction: column;
        width: 100%;
    }

    .main-nav li a {
        text-align: center;
    }
} 

/* 用户信息条美化 */
.user-bar {
    display: flex;
    align-items: center;
    gap: 18px;
    font-size: 15px;
}
.user-name {
    color: var(--primary-color, #2d3748);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}
.user-link {
    color: #4a5568;
    text-decoration: none;
    padding: 2px 8px;
    border-radius: 4px;
    transition: background 0.2s, color 0.2s;
    font-size: 15px;
    background: none;
    border: none;
    cursor: pointer;
    outline: none;
    display: inline-block;
}
.user-link:hover, .user-link:focus {
    color: #c53030;
    background: #fef2f2;
}
.logout-link {
    color: #e53e3e;
    font-weight: 500;
    background: none;
    border: none;
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.logout-link:hover, .logout-link:focus {
    color: #fff;
    background: #e53e3e;
} 