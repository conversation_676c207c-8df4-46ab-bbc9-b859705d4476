<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - e商城</title>
    <link rel="stylesheet" href="~/css/common.css">
    <link rel="icon" type="image/x-icon" href="~/images/icons/favicon.ico">
    @RenderSection("Styles", required: false)
</head>
<body>
    @Html.AntiForgeryToken()
    <!-- 头部 -->
    <header class="header">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="container">
                <div class="top-links" style="display: flex; align-items: center; gap: 20px;">
                    <a href="#" onclick="addToFavorites()">⭐ 收藏本站</a>
                    <a href="/Order">我的订单</a>
                    <a href="#">手机版</a>
                    <a href="#">客户服务</a>
                </div>
                <div class="cart-info" style="display: flex; align-items: center; gap: 24px;">
                    @await Component.InvokeAsync("CartCount")
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </div>
        
        <!-- 主头部 -->
        <div class="main-header">
            <div class="container">
                <a href="/" class="logo">
                    <div class="logo-container">
                        <div class="logo-text">
                            <h1 class="logo-title">e商城</h1>
                            <p class="logo-slogan">多·好·快·省</p>
                        </div>
                    </div>
                </a>
                
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="热门搜索：小米手机、固态硬盘、单反相机、机械键盘、空气净化器">
                    <button class="search-btn" onclick="performSearch()">搜索</button>
                </div>
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
            <div class="container">
                <button class="category-dropdown" id="categoryDropdownBtn" onclick="toggleCategorySidebar()">
                    全部商品分类 ▼
                </button>
                <ul class="main-nav">
                    <li><a href="/">首页</a></li>
                    <li><a href="/Product?category=smart">智能家居</a></li>
                    <li><a href="/Product?category=mobile">手机配件</a></li>
                    <li><a href="/Product?category=flash">闪购</a></li>
                    <li><a href="/Product?category=diy">电脑DIY</a></li>
                    <li><a href="/Product?category=books">图书音像</a></li>
                    <li><a href="/Product?category=sports">运动户外</a></li>
                    <li><a href="/Product?category=department">百货用品</a></li>
                    <li><a href="/Product?category=fashion">服饰鞋包</a></li>
                    
                </ul>
            </div>
        </nav>
    </header>

    <!-- 消息提示 -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success" style="position: fixed; top: 20px; right: 20px; z-index: 10000; padding: 15px; background: #4CAF50; color: white; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            @TempData["SuccessMessage"]
            <button onclick="this.parentElement.style.display='none'" style="background: none; border: none; color: white; float: right; font-size: 18px; cursor: pointer; margin-left: 10px;">&times;</button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-error" style="position: fixed; top: 20px; right: 20px; z-index: 10000; padding: 15px; background: #f44336; color: white; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            @TempData["ErrorMessage"]
            <button onclick="this.parentElement.style.display='none'" style="background: none; border: none; color: white; float: right; font-size: 18px; cursor: pointer; margin-left: 10px;">&times;</button>
        </div>
    }

    @RenderBody()

    <!-- 页脚 -->
    <footer style="background: #2d3748; color: white; padding: 40px 0; margin-top: 60px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px;">
                <div>
                    <h4 style="margin-bottom: 15px;">关于我们</h4>
                    <p style="color: #a0aec0; font-size: 14px; line-height: 1.6;">
                        e商城致力于为用户提供优质的购物体验，多·好·快·省是我们的服务理念。
                    </p>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">客户服务</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">帮助中心</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">售后服务</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">配送说明</a></li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">联系我们</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;">客服热线：400-123-4567</li>
                        <li style="margin-bottom: 8px;">邮箱：<EMAIL></li>
                        <li style="margin-bottom: 8px;">工作时间：9:00-18:00</li>
                    </ul>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #4a5568; color: #a0aec0; font-size: 12px;">
                Copyright © 2025 E-SHOP.COM All Rights Reserved
            </div>
        </div>
    </footer>

    <script src="~/js/common.js"></script>
    <script>
        // 自动隐藏消息提示
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 3000);
            });
        });
    </script>
    @RenderSection("Scripts", required: false)
</body>
</html> 