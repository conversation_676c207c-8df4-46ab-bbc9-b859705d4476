@model ShoppingMall.Web.Models.RegisterViewModel
@{
    ViewData["Title"] = "注册";
    Layout = "_AuthLayout";
}

<div class="auth-page">
    <div class="auth-container">
        <div class="auth-decoration" style="background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);">
            <div class="decoration-content">
                <div class="decoration-icon">🎉</div>
                <h1 class="decoration-title">加入我们</h1>
                <p class="decoration-subtitle">创建您的账户，开启购物新体验。享受个性化推荐，专属优惠和贴心服务。</p>
            </div>
        </div>
        <div class="auth-form-section">
            <div class="auth-header">
                <div class="auth-logo">🛒 购物商城</div>
                <h2 class="auth-title">创建账户</h2>
                <p class="auth-subtitle">请填写以下信息以创建您的账户</p>
            </div>
            <form asp-action="Register" method="post" class="auth-form">
                <div asp-validation-summary="All" class="validation-summary-errors"></div>
                <div class="form-group">
                    <label asp-for="Email">邮箱地址</label>
                    <input asp-for="Email" class="form-input" placeholder="请输入您的邮箱地址" />
                    <span asp-validation-for="Email" class="field-validation-error"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Password">密码</label>
                    <div class="password-input-group">
                        <input asp-for="Password" type="password" class="form-input" placeholder="请设置您的密码" />
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="field-validation-error"></span>
                </div>
                <div class="form-group">
                    <label asp-for="ConfirmPassword">确认密码</label>
                    <div class="password-input-group">
                        <input asp-for="ConfirmPassword" type="password" class="form-input" placeholder="请再次输入密码" />
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="ConfirmPassword" class="field-validation-error"></span>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="agree-terms" required />
                    <label for="agree-terms">我已阅读并同意 <a href="#" style="color: #667eea;">用户协议</a> 和 <a href="#" style="color: #667eea;">隐私政策</a></label>
                </div>
                <button type="submit" class="auth-btn">
                    <i class="fas fa-user-plus" style="margin-right: 8px;"></i>
                    立即注册
                </button>
                <div class="auth-links">
                    已有账户？<a asp-action="Login">
                        <i class="fas fa-sign-in-alt" style="margin-right: 4px;"></i>
                        立即登录
                    </a>
                </div>
                <div class="social-login">
                    <div class="social-title">其他注册方式</div>
                    <div class="social-buttons">
                        <button type="button" class="social-btn">
                            <span class="fab fa-weixin"></span>
                            微信
                        </button>
                        <button type="button" class="social-btn">
                            <span class="fab fa-qq"></span>
                            QQ
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function togglePassword(button) {
            const input = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
} 