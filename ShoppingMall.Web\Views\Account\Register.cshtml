@model ShoppingMall.Web.Models.RegisterViewModel
@{
    ViewData["Title"] = "注册";
    Layout = "_AuthLayout";
}

<div class="auth-page">
    <div class="auth-container">
        <div class="auth-decoration" style="background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);">
            <div class="decoration-content">
                <div class="decoration-icon">🎉</div>
                <h1 class="decoration-title">加入我们</h1>
                <p class="decoration-subtitle">创建您的账户，开启购物新体验</p>
            </div>
        </div>
        <div class="auth-form-section">
            <div class="auth-header">
                <div class="auth-logo">Shopping Mall</div>
                <h2 class="auth-title">注册</h2>
                <p class="auth-subtitle">请填写以下信息创建账户</p>
            </div>
            <form asp-action="Register" method="post" class="auth-form">
                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group">
                    <label asp-for="Email">邮箱</label>
                    <input asp-for="Email" class="form-input" placeholder="请输入邮箱" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Password">密码</label>
                    <div class="password-input-group">
                        <input asp-for="Password" class="form-input" placeholder="请输入密码" />
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="ConfirmPassword">确认密码</label>
                    <div class="password-input-group">
                        <input asp-for="ConfirmPassword" class="form-input" placeholder="请确认密码" />
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                </div>
                <div class="captcha-group">
                    <input type="text" class="form-input captcha-input" placeholder="请输入验证码" />
                    <div class="captcha-image">1234</div>
                </div>
                <a href="#" class="captcha-refresh">看不清？换一张</a>
                <button type="submit" class="auth-btn">注册</button>
                <div class="auth-links">
                    已有账户？<a asp-action="Login">立即登录</a>
                </div>
                <div class="social-login">
                    <div class="social-title">其他注册方式</div>
                    <div class="social-buttons">
                        <button type="button" class="social-btn">
                            <span class="fab fa-weixin"></span>
                            微信
                        </button>
                        <button type="button" class="social-btn">
                            <span class="fab fa-qq"></span>
                            QQ
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function togglePassword(button) {
            const input = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
} 