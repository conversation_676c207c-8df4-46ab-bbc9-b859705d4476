@using Microsoft.AspNetCore.Identity

@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

@if (SignInManager.IsSignedIn(User))
{
    <div class="user-bar">
        <span class="user-name"><i class="fas fa-user-circle"></i> @User.Identity?.Name</span>
        <a asp-controller="Account" asp-action="Profile" class="user-link">个人中心</a>
        <a asp-controller="Order" asp-action="Index" class="user-link">我的订单</a>
        <a asp-controller="Account" asp-action="Settings" class="user-link">账户设置</a>
        <form asp-controller="Account" asp-action="Logout" method="post" style="display:inline;">
            <button type="submit" class="user-link logout-link">退出登录</button>
        </form>
    </div>
}
else
{
    <div class="user-bar">
        <a asp-controller="Account" asp-action="Login" class="user-link">登录</a>
        <a asp-controller="Account" asp-action="Register" class="user-link">注册</a>
    </div>
} 