{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/auth.8vostw0cvf.css", "AssetFile": "css/auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5ClmIHFMH4UU23PlXMD+loOG91Di/dHAslkFzmZ0el4=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 06:22:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8vostw0cvf"}, {"Name": "integrity", "Value": "sha256-5ClmIHFMH4UU23PlXMD+loOG91Di/dHAslkFzmZ0el4="}, {"Name": "label", "Value": "css/auth.css"}]}, {"Route": "css/auth.css", "AssetFile": "css/auth.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4694"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5ClmIHFMH4UU23PlXMD+loOG91Di/dHAslkFzmZ0el4=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 06:22:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5ClmIHFMH4UU23PlXMD+loOG91Di/dHAslkFzmZ0el4="}]}, {"Route": "css/cart.css", "AssetFile": "css/cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs="}]}, {"Route": "css/cart.qhsnkupta7.css", "AssetFile": "css/cart.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qhsnkupta7"}, {"Name": "integrity", "Value": "sha256-AnTTtcwpWD3HLkyWqVS6P8QmFehJQTrnYerE8BheRPs="}, {"Name": "label", "Value": "css/cart.css"}]}, {"Route": "css/common.css", "AssetFile": "css/common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6356"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:29:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw="}]}, {"Route": "css/common.k6t8vmrr5t.css", "AssetFile": "css/common.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6356"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:29:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6t8vmrr5t"}, {"Name": "integrity", "Value": "sha256-RLN08/7f15ClA9OwJ9alRhcDK4S/H9ftPBbGw0DQzxw="}, {"Name": "label", "Value": "css/common.css"}]}, {"Route": "css/index.css", "AssetFile": "css/index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:36:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY="}]}, {"Route": "css/index.i7jnjijmz4.css", "AssetFile": "css/index.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6171"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:36:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i7jnjijmz4"}, {"Name": "integrity", "Value": "sha256-/usRXsXMCD84OdS0G7ssbLA4mjpixZ99VCmdaxM3JVY="}, {"Name": "label", "Value": "css/index.css"}]}, {"Route": "css/product-details.c84xq4nezm.css", "AssetFile": "css/product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c84xq4nezm"}, {"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}, {"Name": "label", "Value": "css/product-details.css"}]}, {"Route": "css/product-details.css", "AssetFile": "css/product-details.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9385"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VJLGjzLl8gD645yAOCnLU8l1NleraXVjUKvGt1vlOaA="}]}, {"Route": "css/product-list.css", "AssetFile": "css/product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13548"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BWDc2yVIORWj4xELqjpcJQDy0rdMhkCK3ZHV+8Bdwbg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:34:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BWDc2yVIORWj4xELqjpcJQDy0rdMhkCK3ZHV+8Bdwbg="}]}, {"Route": "css/product-list.nt99mo9h8y.css", "AssetFile": "css/product-list.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13548"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BWDc2yVIORWj4xELqjpcJQDy0rdMhkCK3ZHV+8Bdwbg=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:34:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nt99mo9h8y"}, {"Name": "integrity", "Value": "sha256-BWDc2yVIORWj4xELqjpcJQDy0rdMhkCK3ZHV+8Bdwbg="}, {"Name": "label", "Value": "css/product-list.css"}]}, {"Route": "images/banners/bxite-F2015051415340001.jpg", "AssetFile": "images/banners/bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}]}, {"Route": "images/banners/bxite-F2015051415340001.qokh4uiflp.jpg", "AssetFile": "images/banners/bxite-F2015051415340001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "169333"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qokh4uiflp"}, {"Name": "integrity", "Value": "sha256-BuFvtUZKSWjKigVixo8GNvEuVXztTIqSceINw9oRVNk="}, {"Name": "label", "Value": "images/banners/bxite-F2015051415340001.jpg"}]}, {"Route": "images/banners/bxite-F2015051416060001.hw1ap2h7wz.jpg", "AssetFile": "images/banners/bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hw1ap2h7wz"}, {"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}, {"Name": "label", "Value": "images/banners/bxite-F2015051416060001.jpg"}]}, {"Route": "images/banners/bxite-F2015051416060001.jpg", "AssetFile": "images/banners/bxite-F2015051416060001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "415631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wZjY9xuMjj8ctz5V39O+arpY24zA+0ZX+e81+eTV7c="}]}, {"Route": "images/banners/bxite-F2015051416070001.8co0co73td.jpg", "AssetFile": "images/banners/bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8co0co73td"}, {"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070001.jpg"}]}, {"Route": "images/banners/bxite-F2015051416070001.jpg", "AssetFile": "images/banners/bxite-F2015051416070001.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "277459"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OJcY1tMd3/ZXR3UYer5HndI+Im9t5f0IgEXEVfRIp9U="}]}, {"Route": "images/banners/bxite-F2015051416070002.jpg", "AssetFile": "images/banners/bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}]}, {"Route": "images/banners/bxite-F2015051416070002.t66wtv88gg.jpg", "AssetFile": "images/banners/bxite-F2015051416070002.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "185954"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t66wtv88gg"}, {"Name": "integrity", "Value": "sha256-VYACJwzlWtAfsS8t868R3dl/d5uBhf45wqVirIHeh2U="}, {"Name": "label", "Value": "images/banners/bxite-F2015051416070002.jpg"}]}, {"Route": "images/icons/favicon.7duv211uso.ico", "AssetFile": "images/icons/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25214"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 12:50:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7duv211uso"}, {"Name": "integrity", "Value": "sha256-hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg="}, {"Name": "label", "Value": "images/icons/favicon.ico"}]}, {"Route": "images/icons/favicon.ico", "AssetFile": "images/icons/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25214"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 12:50:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hd7ElSPtqMeVJ8Q85RbVyyhx/8dN0YbCGkXJM/a80dg="}]}, {"Route": "images/products/CPU.jpg", "AssetFile": "images/products/CPU.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}]}, {"Route": "images/products/CPU.tfepd08b58.jpg", "AssetFile": "images/products/CPU.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16670"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tfepd08b58"}, {"Name": "integrity", "Value": "sha256-p31mAii41mWEOb4AK5YBVGlu8v+gEulUMdNIhLdJwxE="}, {"Name": "label", "Value": "images/products/CPU.jpg"}]}, {"Route": "images/products/WDyinpan.jpg", "AssetFile": "images/products/WDyinpan.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}]}, {"Route": "images/products/WDyinpan.xdjj6mqi2s.jpg", "AssetFile": "images/products/WDyinpan.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22474"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xdjj6mqi2s"}, {"Name": "integrity", "Value": "sha256-5eZbs8lS4jlk+Ds2mKOw0BKemgvzcZxlPKJto/q0gZI="}, {"Name": "label", "Value": "images/products/WDyinpan.jpg"}]}, {"Route": "images/products/dianshi.jpg", "AssetFile": "images/products/dianshi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}]}, {"Route": "images/products/dianshi.mcac68kan4.jpg", "AssetFile": "images/products/dianshi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33984"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mcac68kan4"}, {"Name": "integrity", "Value": "sha256-ZmWaDo/tCoB1tqscpoYAJHJfyIVdvrsPKkiDukTrC4k="}, {"Name": "label", "Value": "images/products/dianshi.jpg"}]}, {"Route": "images/products/luyouqi.1j99jfbvzg.jpg", "AssetFile": "images/products/luyouqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1j99jfbvzg"}, {"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}, {"Name": "label", "Value": "images/products/luyouqi.jpg"}]}, {"Route": "images/products/luyouqi.jpg", "AssetFile": "images/products/luyouqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "15492"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g2jQD6Kr6xm/9LT1/kBI418M3w+cn5T5HMIE5M7ZIZ4="}]}, {"Route": "images/products/shouji.6i8a2md2c7.jpg", "AssetFile": "images/products/shouji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6i8a2md2c7"}, {"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}, {"Name": "label", "Value": "images/products/shouji.jpg"}]}, {"Route": "images/products/shouji.jpg", "AssetFile": "images/products/shouji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "26917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cgzzv89SaSqm+iGHtc73PWnkrnhL76U0zBsQgEQ6cJ0="}]}, {"Route": "images/products/shouji2.jpg", "AssetFile": "images/products/shouji2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}]}, {"Route": "images/products/shouji2.kcc1gx74e1.jpg", "AssetFile": "images/products/shouji2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26366"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kcc1gx74e1"}, {"Name": "integrity", "Value": "sha256-xJj+kkrZAPE5hLNeKTb9wfSjqUJzy3amTU8i0NogTLI="}, {"Name": "label", "Value": "images/products/shouji2.jpg"}]}, {"Route": "images/products/touyingyi.jpg", "AssetFile": "images/products/touyingyi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}]}, {"Route": "images/products/touyingyi.tvh7n3sxrd.jpg", "AssetFile": "images/products/touyingyi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22963"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tvh7n3sxrd"}, {"Name": "integrity", "Value": "sha256-SbndDOlr4NUVtEG+AE3DwsqxiPiqX3doMDy7fV0h9d0="}, {"Name": "label", "Value": "images/products/touyingyi.jpg"}]}, {"Route": "images/products/win7.e1qt27ox10.jpg", "AssetFile": "images/products/win7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1qt27ox10"}, {"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}, {"Name": "label", "Value": "images/products/win7.jpg"}]}, {"Route": "images/products/win7.jpg", "AssetFile": "images/products/win7.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25408"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ljsZC0KmES7jy9okbWpQmkC+V5UKikxZ5sCaaS73kM="}]}, {"Route": "images/products/xiangji.jpg", "AssetFile": "images/products/xiangji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}]}, {"Route": "images/products/xiangji.tx7hrwy3ub.jpg", "AssetFile": "images/products/xiangji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "27056"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tx7hrwy3ub"}, {"Name": "integrity", "Value": "sha256-cZ15HmOU1WoCS214VAlirrLFd3YM4ziIxIKIriCBX3c="}, {"Name": "label", "Value": "images/products/xiangji.jpg"}]}, {"Route": "images/products/xianka.9xalzd3pbe.jpg", "AssetFile": "images/products/xianka.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xalzd3pbe"}, {"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}, {"Name": "label", "Value": "images/products/xianka.jpg"}]}, {"Route": "images/products/xianka.jpg", "AssetFile": "images/products/xianka.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "22367"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cw6BiOSkRxK5Y+UcNeO4BBCdACd6UprZZsRMd3UyVk8="}]}, {"Route": "images/products/xianshiqi.jpg", "AssetFile": "images/products/xianshiqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}]}, {"Route": "images/products/xianshiqi.vcnlt9rpyp.jpg", "AssetFile": "images/products/xianshiqi.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25803"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vcnlt9rpyp"}, {"Name": "integrity", "Value": "sha256-gaUIRDEIxI2+XVcNDXIM16JiacDHlOFOtKlkp/E8nJE="}, {"Name": "label", "Value": "images/products/xianshiqi.jpg"}]}, {"Route": "images/products/zhuban.jpg", "AssetFile": "images/products/zhuban.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}]}, {"Route": "images/products/zhuban.u6l54bdsf3.jpg", "AssetFile": "images/products/zhuban.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "35357"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u6l54bdsf3"}, {"Name": "integrity", "Value": "sha256-hlq9b+f90BlYwQOyMCuhwmh6Y9L3m8D+7UhTrlZPoTM="}, {"Name": "label", "Value": "images/products/zhuban.jpg"}]}, {"Route": "images/products/zhuji.jpg", "AssetFile": "images/products/zhuji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}]}, {"Route": "images/products/zhuji.m0xi4xxp31.jpg", "AssetFile": "images/products/zhuji.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "58073"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 14:04:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m0xi4xxp31"}, {"Name": "integrity", "Value": "sha256-WpDQdGFDcDjoNJxjIj6TK1bYiCvgdiPKNXXuOXuKMjI="}, {"Name": "label", "Value": "images/products/zhuji.jpg"}]}, {"Route": "js/carousel.4td0zeg1u6.js", "AssetFile": "js/carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4td0zeg1u6"}, {"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}, {"Name": "label", "Value": "js/carousel.js"}]}, {"Route": "js/carousel.js", "AssetFile": "js/carousel.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1162"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 16:06:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GH9kiciQ2ZeFV/B+hvfC6D4/DrBJ+rG48PggxiXszhs="}]}, {"Route": "js/common.6d6ohnsy7j.js", "AssetFile": "js/common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2942"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OuM2X8qw/eFjDmyOqDZ76QBOz9A+fZt2gBkrK9RtuSs=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 05:52:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6d6ohnsy7j"}, {"Name": "integrity", "Value": "sha256-OuM2X8qw/eFjDmyOqDZ76QBOz9A+fZt2gBkrK9RtuSs="}, {"Name": "label", "Value": "js/common.js"}]}, {"Route": "js/common.js", "AssetFile": "js/common.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2942"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OuM2X8qw/eFjDmyOqDZ76QBOz9A+fZt2gBkrK9RtuSs=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 05:52:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OuM2X8qw/eFjDmyOqDZ76QBOz9A+fZt2gBkrK9RtuSs="}]}]}