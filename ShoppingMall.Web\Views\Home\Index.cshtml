@model HomeViewModel
@{
    ViewData["Title"] = "首页";
}

@section Styles {
    <link rel="stylesheet" href="~/css/index.css">
}

<!-- 主要内容 -->
<main class="container">

    
    <div class="main-content" id="mainContent">
        <!-- 左侧分类菜单 -->
        <aside class="sidebar" id="categorySidebar">
            <div class="category-menu">
                <div class="category-header">
                    <h3 class="category-title">所有类目</h3>
                </div>
                <ul class="category-list">
                    <li>
                        <a href="/Product?category=computer">
                            <span>电脑配件</span>
                            <span class="subcategories">CPU 主板</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=mobile">
                            <span>手机配件</span>
                            <span class="subcategories">电池/膜 蓝牙耳机</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=digital">
                            <span>数码影音</span>
                            <span class="subcategories">数码相机 摄像/摄</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=home">
                            <span>家用电器</span>
                            <span class="subcategories">平板电脑 空调</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=office">
                            <span>办公打印</span>
                            <span class="subcategories">投影机 多功能一</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=network">
                            <span>网络产品</span>
                            <span class="subcategories">路由器 网卡</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=external">
                            <span>外设产品</span>
                            <span class="subcategories">鼠标 键盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=service">
                            <span>服务产品</span>
                            <span class="subcategories">上门服务 远程服务</span>
                        </a>
                    </li>
                    <!-- 新增衣食住行相关分类 -->
                    <li>
                        <a href="/Product?category=books">
                            <span>图书音像</span>
                            <span class="subcategories">小说 文学 教辅</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=sports">
                            <span>运动户外</span>
                            <span class="subcategories">健身器材 户外装备</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=department">
                            <span>百货用品</span>
                            <span class="subcategories">厨房 清洁 日用</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=fashion">
                            <span>服饰鞋包</span>
                            <span class="subcategories">男装 女装 鞋靴 箱包</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=food">
                            <span>食品饮料</span>
                            <span class="subcategories">休闲零食 饮料冲调</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=beauty">
                            <span>美妆个护</span>
                            <span class="subcategories">护肤 彩妆 洗护</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=homeimprove">
                            <span>家装家纺</span>
                            <span class="subcategories">家纺 家具 装修</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=motherbaby">
                            <span>母婴用品</span>
                            <span class="subcategories">奶粉 纸尿裤 玩具</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=car">
                            <span>汽车用品</span>
                            <span class="subcategories">车载电器 养护</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- 主内容区 -->
        <div class="content-area">
            <!-- 轮播图 -->
            <section class="hero-section">
                <div class="carousel" id="mainCarousel">
                    <div class="carousel-slide active">
                        <img src="~/images/banners/bxite-F2015051415340001.jpg" alt="HyperX SSD 闪世敌利">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416060001.jpg" alt="促销活动">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416070001.jpg" alt="新品推荐">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416070002.jpg" alt="特价商品">
                    </div>
                    
                    <!-- 轮播指示器 -->
                    <div class="carousel-indicators">
                        <button class="carousel-indicator active" onclick="goToSlide(0)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(1)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(2)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(3)"></button>
                    </div>
                    
                    <!-- 轮播导航 -->
                    <button class="carousel-nav carousel-prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav carousel-next" onclick="nextSlide()">›</button>
                </div>
            </section>

            <!-- 热品推荐 -->
            <section class="products-section">
                <div class="section-header">
                    <h2 class="section-title">热品推荐</h2>
                    <a href="/Product" class="section-more">查看更多 →</a>
                </div>
                <div class="products-grid">
                    @foreach (var product in Model.HotProducts)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                <a href="@Url.Action("Details", "Product", new { id = product.Id })">
                                    <img src="@product.ImageUrl" alt="@product.Name">
                                </a>
                                @if (product.IsHot)
                                {
                                    <span class="product-badge">热销</span>
                                }
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">
                                    <a href="@Url.Action("Details", "Product", new { id = product.Id })">@product.Name</a>
                                </h3>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                </div>
                                <div class="product-actions">
                                    <button class="add-to-cart" onclick="addToCart(@product.Id)">加入购物车</button>
                                    <button class="add-to-wishlist" onclick="addToWishlist(@product.Id)">♡</button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </section>

            <!-- 电脑配件分类 -->
            <section class="products-section">
                <div class="section-header">
                    <h2 class="section-title">电脑配件</h2>
                    <a href="/Product?category=computer" class="section-more">查看更多 →</a>
                </div>
                <div class="products-grid">
                    @foreach (var product in Model.ComputerProducts)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                <a href="@Url.Action("Details", "Product", new { id = product.Id })">
                                    <img src="@product.ImageUrl" alt="@product.Name">
                                </a>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">
                                    <a href="@Url.Action("Details", "Product", new { id = product.Id })">@product.Name</a>
                                </h3>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                </div>
                                <div class="product-actions">
                                    <button class="add-to-cart" onclick="addToCart(@product.Id)">加入购物车</button>
                                    <button class="add-to-wishlist" onclick="addToWishlist(@product.Id)">♡</button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </section>
        </div>
    </div>
</main>

<!-- 隐藏的防伪Token表单，供JS获取token用 -->
<form id="antiForgeryForm" style="display:none;">
    @Html.AntiForgeryToken()
</form>

@section Scripts {
    <script src="~/js/carousel.js"></script>
    <script>
        function toggleCategorySidebar() {
            const sidebar = document.getElementById('categorySidebar');
            const mainContent = document.getElementById('mainContent');
            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
        }
        // 页面加载时默认显示侧边栏
        window.addEventListener('DOMContentLoaded', function() {
            document.getElementById('categorySidebar').classList.remove('hidden');
            document.getElementById('mainContent').classList.remove('sidebar-hidden');
        });
    </script>
} 