@model HomeViewModel
@{
    ViewData["Title"] = "首页";
}

@section Styles {
    <link rel="stylesheet" href="~/css/index.css">
}

<!-- 主要内容 -->
<main class="container">
    <div class="main-content">
        <!-- 左侧分类菜单 -->
        <aside class="sidebar">
            <div class="category-menu">
                <h3>所有类目</h3>
                <ul class="category-list">
                    <li>
                        <a href="/Product?category=computer">
                            <span>电脑配件</span>
                            <span class="subcategories">CPU 主板</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=mobile">
                            <span>手机配件</span>
                            <span class="subcategories">电池/膜 蓝牙耳机</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=digital">
                            <span>数码影音</span>
                            <span class="subcategories">数码相机 摄像/摄</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=home">
                            <span>家用电器</span>
                            <span class="subcategories">平板电脑 空调</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=office">
                            <span>办公打印</span>
                            <span class="subcategories">投影机 多功能一</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=network">
                            <span>网络产品</span>
                            <span class="subcategories">路由器 网卡</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=external">
                            <span>外设产品</span>
                            <span class="subcategories">鼠标 键盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="/Product?category=service">
                            <span>服务产品</span>
                            <span class="subcategories">上门服务 远程服务</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- 主内容区 -->
        <div class="content-area">
            <!-- 轮播图 -->
            <section class="hero-section">
                <div class="carousel" id="mainCarousel">
                    <div class="carousel-slide active">
                        <img src="~/images/banners/bxite-F2015051415340001.jpg" alt="HyperX SSD 闪世敌利">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416060001.jpg" alt="促销活动">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416070001.jpg" alt="新品推荐">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/bxite-F2015051416070002.jpg" alt="特价商品">
                    </div>
                    
                    <!-- 轮播指示器 -->
                    <div class="carousel-indicators">
                        <button class="carousel-indicator active" onclick="goToSlide(0)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(1)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(2)"></button>
                        <button class="carousel-indicator" onclick="goToSlide(3)"></button>
                    </div>
                    
                    <!-- 轮播导航 -->
                    <button class="carousel-nav carousel-prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav carousel-next" onclick="nextSlide()">›</button>
                </div>
            </section>

            <!-- 热品推荐 -->
            <section class="products-section">
                <div class="section-header">
                    <h2 class="section-title">热品推荐</h2>
                    <a href="/Product" class="section-more">查看更多 →</a>
                </div>
                <div class="products-grid">
                    @foreach (var product in Model.HotProducts)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                <img src="@product.ImageUrl" alt="@product.Name">
                                @if (product.IsHot)
                                {
                                    <span class="product-badge">热销</span>
                                }
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">@product.Name</h3>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                </div>
                                <div class="product-actions">
                                    <button class="add-to-cart" onclick="addToCart(@product.Id)">加入购物车</button>
                                    <button class="add-to-wishlist" onclick="addToWishlist(@product.Id)">♡</button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </section>

            <!-- 电脑配件分类 -->
            <section class="products-section">
                <div class="section-header">
                    <h2 class="section-title">电脑配件</h2>
                    <a href="/Product?category=computer" class="section-more">查看更多 →</a>
                </div>
                <div class="products-grid">
                    @foreach (var product in Model.ComputerProducts)
                    {
                        <div class="product-card">
                            <div class="product-image">
                                <img src="@product.ImageUrl" alt="@product.Name">
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">@product.Name</h3>
                                <div class="product-price">
                                    <span class="current-price">¥@product.Price.ToString("F2")</span>
                                </div>
                                <div class="product-actions">
                                    <button class="add-to-cart" onclick="addToCart(@product.Id)">加入购物车</button>
                                    <button class="add-to-wishlist" onclick="addToWishlist(@product.Id)">♡</button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </section>
        </div>
    </div>
</main>

@section Scripts {
    <script src="~/js/carousel.js"></script>
} 