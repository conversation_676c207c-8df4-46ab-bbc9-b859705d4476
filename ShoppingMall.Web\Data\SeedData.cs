using Microsoft.EntityFrameworkCore;
using ShoppingMall.Web.Models;

namespace ShoppingMall.Web.Data;

public static class SeedData
{
    public static async Task InitializeAsync(ApplicationDbContext context)
    {
        // 确保数据库已创建
        await context.Database.EnsureCreatedAsync();

        // 如果已有产品数据，则不需要种子数据
        if (await context.Products.AnyAsync())
        {
            return;
        }

        var products = new List<Product>
        {
            new Product
            {
                Name = "神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机",
                Description = "高性能台式机，适合游戏与办公，搭载AMD860K处理器、4G内存、2G独显和120G固态硬盘。",
                Price = 1788.00m,
                ImageUrl = "/images/products/zhuji.jpg",
                Stock = 20,
                IsAvailable = true,
                IsHot = true,
                Category = "computer",
                Brand = "神舟",
                Specifications = "CPU: AMD860K, 内存: 4G, 显卡: 2G独显, 硬盘: 120G SSD",
                CreatedAt = DateTime.Now.AddDays(-10),
                ViewCount = 1200,
                SalesCount = 80
            },
            new Product
            {
                Name = "乐视（H60-L01）配置 蓝白色 移动4G手机",
                Description = "乐视4G手机，蓝白配色，支持移动4G网络，性能稳定，外观时尚。",
                Price = 1699.00m,
                ImageUrl = "/images/products/shouji.jpg",
                Stock = 30,
                IsAvailable = true,
                IsHot = true,
                Category = "mobile",
                Brand = "乐视",
                Specifications = "型号: H60-L01, 网络: 移动4G, 颜色: 蓝白",
                CreatedAt = DateTime.Now.AddDays(-8),
                ViewCount = 900,
                SalesCount = 60
            },
            new Product
            {
                Name = "尼康（Nikon）D3200 单反相机套机（AF-S DX）",
                Description = "尼康D3200单反相机，配备AF-S DX镜头，适合摄影爱好者和入门用户。",
                Price = 3309.00m,
                ImageUrl = "/images/products/xiangji.jpg",
                Stock = 15,
                IsAvailable = true,
                IsHot = false,
                Category = "digital",
                Brand = "尼康",
                Specifications = "型号: D3200, 镜头: AF-S DX",
                CreatedAt = DateTime.Now.AddDays(-7),
                ViewCount = 600,
                SalesCount = 30
            },
            new Product
            {
                Name = "酷青(cocoa)K5U 50 蓝牙智能运动手环蓝牙",
                Description = "智能运动手环，支持蓝牙连接，健康监测，适合运动健身。",
                Price = 2799.00m,
                ImageUrl = "/images/products/shouji2.jpg",
                Stock = 25,
                IsAvailable = true,
                IsHot = false,
                Category = "sports",
                Brand = "酷青",
                Specifications = "型号: K5U 50, 功能: 蓝牙, 健康监测",
                CreatedAt = DateTime.Now.AddDays(-6),
                ViewCount = 500,
                SalesCount = 20
            },
            new Product
            {
                Name = "明基（BenQ）MS3081 投影仪会议投影机",
                Description = "明基MS3081投影仪，适合会议和家庭影院，画质清晰，操作简便。",
                Price = 2199.00m,
                ImageUrl = "/images/products/touyingyi.jpg",
                Stock = 18,
                IsAvailable = true,
                IsHot = false,
                Category = "office",
                Brand = "明基",
                Specifications = "型号: MS3081, 用途: 会议/家庭影院",
                CreatedAt = DateTime.Now.AddDays(-5),
                ViewCount = 400,
                SalesCount = 15
            },
            new Product
            {
                Name = "TP-LINK TL-WR886N 4 50M无线路由器（写意白）",
                Description = "TP-LINK无线路由器，450M高速，信号稳定，适合家庭和办公使用。",
                Price = 99.00m,
                ImageUrl = "/images/products/luyouqi.jpg",
                Stock = 50,
                IsAvailable = true,
                IsHot = false,
                Category = "network",
                Brand = "TP-LINK",
                Specifications = "型号: TL-WR886N, 速度: 450M, 颜色: 白",
                CreatedAt = DateTime.Now.AddDays(-4),
                ViewCount = 300,
                SalesCount = 25
            },
            new Product
            {
                Name = "欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机",
                Description = "欣睿宇台式机，酷睿i3处理器，4G内存，1TB硬盘，独立显卡，性能优越。",
                Price = 1659.00m,
                ImageUrl = "/images/products/zhuban.jpg",
                Stock = 10,
                IsAvailable = true,
                IsHot = false,
                Category = "computer",
                Brand = "欣睿宇",
                Specifications = "CPU: i3 4160, 内存: 4G, 硬盘: 1TB, 显卡: 独显",
                CreatedAt = DateTime.Now.AddDays(-3),
                ViewCount = 200,
                SalesCount = 10
            }
        };

        await context.Products.AddRangeAsync(products);
        await context.SaveChangesAsync();
    }
}
