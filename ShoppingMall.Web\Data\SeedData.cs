using Microsoft.EntityFrameworkCore;
using ShoppingMall.Web.Models;

namespace ShoppingMall.Web.Data;

public static class SeedData
{
    public static async Task InitializeAsync(ApplicationDbContext context)
    {
        // 确保数据库已创建
        await context.Database.EnsureCreatedAsync();

        // 如果已有产品数据，则不需要种子数据
        if (await context.Products.AnyAsync())
        {
            return;
        }

        var products = new List<Product>
        {
            new Product
            {
                Name = "HyperX Fury DDR4 内存条 16GB",
                Description = "高性能游戏内存，DDR4-3200MHz，低延迟设计，适合游戏和专业应用",
                Price = 599.00m,
                OriginalPrice = 699.00m,
                ImageUrl = "/images/products/bxite-G2015051717070001.jpg",
                Stock = 50,
                IsAvailable = true,
                IsHot = true,
                Category = "computer",
                Brand = "HyperX",
                Specifications = "容量：16GB，频率：3200MHz，时序：CL16",
                CreatedAt = DateTime.Now.AddDays(-10),
                ViewCount = 1250,
                SalesCount = 89
            },
            new Product
            {
                Name = "罗技 MX Master 3 无线鼠标",
                Description = "专业级无线鼠标，精准追踪，多设备连接，适合办公和设计",
                Price = 699.00m,
                ImageUrl = "/images/products/bxite-G2015051800550001.jpg",
                Stock = 30,
                IsAvailable = true,
                IsHot = true,
                Category = "external",
                Brand = "罗技",
                Specifications = "连接方式：无线，DPI：4000，电池续航：70天",
                CreatedAt = DateTime.Now.AddDays(-8),
                ViewCount = 980,
                SalesCount = 67
            },
            new Product
            {
                Name = "三星 980 PRO NVMe SSD 1TB",
                Description = "高速固态硬盘，PCIe 4.0接口，读取速度高达7000MB/s",
                Price = 899.00m,
                OriginalPrice = 1099.00m,
                ImageUrl = "/images/products/bxite-G2015051801210001.jpg",
                Stock = 25,
                IsAvailable = true,
                IsHot = true,
                Category = "computer",
                Brand = "三星",
                Specifications = "容量：1TB，接口：PCIe 4.0 NVMe，读取：7000MB/s",
                CreatedAt = DateTime.Now.AddDays(-5),
                ViewCount = 1580,
                SalesCount = 123
            },
            new Product
            {
                Name = "华硕 ROG Strix B550-F 主板",
                Description = "AMD B550芯片组主板，支持PCIe 4.0，RGB灯效，适合游戏装机",
                Price = 1299.00m,
                ImageUrl = "/images/products/bxite-G2015051801300001.jpg",
                Stock = 15,
                IsAvailable = true,
                Category = "computer",
                Brand = "华硕",
                Specifications = "芯片组：AMD B550，接口：AM4，内存：DDR4-4400",
                CreatedAt = DateTime.Now.AddDays(-3),
                ViewCount = 756,
                SalesCount = 34
            },
            new Product
            {
                Name = "雷蛇 BlackWidow V3 机械键盘",
                Description = "专业游戏机械键盘，雷蛇绿轴，RGB背光，防水设计",
                Price = 899.00m,
                ImageUrl = "/images/products/bxite-G2015051801390001.jpg",
                Stock = 40,
                IsAvailable = true,
                IsHot = true,
                Category = "external",
                Brand = "雷蛇",
                Specifications = "轴体：雷蛇绿轴，背光：RGB，连接：USB-C",
                CreatedAt = DateTime.Now.AddDays(-2),
                ViewCount = 892,
                SalesCount = 56
            },
            new Product
            {
                Name = "英伟达 RTX 4070 显卡",
                Description = "高性能游戏显卡，支持光线追踪和DLSS 3.0技术",
                Price = 4599.00m,
                ImageUrl = "/images/products/bxite-G2015051801470001.jpg",
                Stock = 8,
                IsAvailable = true,
                IsHot = true,
                Category = "computer",
                Brand = "英伟达",
                Specifications = "显存：12GB GDDR6X，核心：Ada Lovelace，功耗：200W",
                CreatedAt = DateTime.Now.AddDays(-1),
                ViewCount = 2340,
                SalesCount = 78
            }
        };

        await context.Products.AddRangeAsync(products);
        await context.SaveChangesAsync();
    }
}
